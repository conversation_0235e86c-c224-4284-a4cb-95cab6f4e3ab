package com.teach.javafx.controller;

import com.teach.javafx.MainApplication;
import com.teach.javafx.controller.base.LocalDateStringConverter;
import com.teach.javafx.controller.base.ToolController;
import com.teach.javafx.request.*;
import javafx.scene.Scene;
import javafx.scene.control.cell.TextFieldTableCell;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.FlowPane;
import javafx.stage.Modality;
import javafx.stage.Stage;
import com.teach.javafx.request.DataRequest;
import com.teach.javafx.request.DataResponse;
import com.teach.javafx.util.CommonMethod;
import com.teach.javafx.controller.base.MessageDialog;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.MapValueFactory;
import javafx.stage.FileChooser;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.layout.BorderPane;
import javafx.stage.Stage;

/**
 * StudentController 登录交互控制类 对应 student_panel.fxml  对应于学生管理的后台业务处理的控制器，主要获取数据和保存数据的方法不同
 *
 * @FXML 属性 对应fxml文件中的
 * @FXML 方法 对应于fxml文件中的 on***Click的属性
 */
public class TeacherController extends ToolController {
    // 照片功能已移除
    @FXML
    private TableView<Map> dataTableView;  //学生信息表
    @FXML
    private TableColumn<Map, String> numColumn;   //学生信息表 编号列
    @FXML
    private TableColumn<Map, String> nameColumn; //学生信息表 名称列
    @FXML
    private TableColumn<Map, String> deptColumn;  //学生信息表 院系列
    @FXML
    private TableColumn<Map, String> titleColumn; //学生信息表 专业列
    @FXML
    private TableColumn<Map, String> degreeColumn; //学生信息表 班级列
    @FXML
    private TableColumn<Map, String> cardColumn; //学生信息表 证件号码列
    @FXML
    private TableColumn<Map, String> genderColumn; //学生信息表 性别列
    @FXML
    private TableColumn<Map, String> birthdayColumn; //学生信息表 出生日期列
    @FXML
    private TableColumn<Map, String> emailColumn; //学生信息表 邮箱列
    @FXML
    private TableColumn<Map, String> phoneColumn; //学生信息表 电话列
    @FXML
    private TableColumn<Map, String> addressColumn;//学生信息表 地址列


    @FXML
    private TextField numField; //学生信息  学号输入域
    @FXML
    private TextField nameField;  //学生信息  名称输入域
    @FXML
    private TextField deptField; //学生信息  院系输入域
    @FXML
    private TextField titleField; //学生信息  专业输入域
    @FXML
    private TextField degreeField; //学生信息  班级输入域
    @FXML
    private TextField cardField; //学生信息  证件号码输入域
    @FXML
    private ComboBox<OptionItem> genderComboBox;  //学生信息  性别输入域
    @FXML
    private DatePicker birthdayPick;  //学生信息  出生日期选择域
    @FXML
    private TextField emailField;  //学生信息  邮箱输入域
    @FXML
    private TextField phoneField;   //学生信息  电话输入域
    @FXML
    private TextField addressField;  //学生信息  地址输入域
    @FXML
    private DatePicker enterTimePick; //教师信息  入职时间选择域
    @FXML
    private TableColumn<Map, String> enterTimeColumn; //教师信息  入职时间列

    @FXML
    private TextField numNameTextField;  //查询 姓名学号输入域

    private Integer personId = null;  //当前编辑修改的学生的主键

    private ArrayList<Map> teacherList = new ArrayList();  // 学生信息列表数据
    private List<OptionItem> genderList;   //性别选择列表数据
    private ObservableList<Map> observableList = FXCollections.observableArrayList();  // TableView渲染列表


    /**
     * 将学生数据集合设置到面板上显示
     */
    private void setTableViewData() {
        observableList.clear();
        for (int j = 0; j < teacherList.size(); j++) {
            observableList.addAll(FXCollections.observableArrayList(teacherList.get(j)));
        }
        dataTableView.setItems(observableList);
    }

    /**
     * 页面加载对象创建完成初始化方法，页面中控件属性的设置，初始数据显示等初始操作都在这里完成，其他代码都事件处理方法里
     */

    @FXML
    public void initialize() {
        // 照片功能已移除
        DataResponse res;
        DataRequest req = new DataRequest();
        req.add("numName", "");
        res = HttpRequestUtil.request("/api/teacher/getTeacherList", req); //从后台获取所有教师信息列表集合
        if (res != null && res.getCode() == 0) {
            teacherList = (ArrayList<Map>) res.getData();
        }
        numColumn.setCellValueFactory(new MapValueFactory<>("num"));  //设置列值工程属性
        nameColumn.setCellValueFactory(new MapValueFactory<>("name"));//设置列值工程属性
        deptColumn.setCellValueFactory(new MapValueFactory<>("dept"));
        titleColumn.setCellValueFactory(new MapValueFactory<>("title"));
        degreeColumn.setCellValueFactory(new MapValueFactory<>("degree"));
        cardColumn.setCellValueFactory(new MapValueFactory<>("card"));
        genderColumn.setCellValueFactory(new MapValueFactory<>("genderName"));
        birthdayColumn.setCellValueFactory(new MapValueFactory<>("birthday"));
        emailColumn.setCellValueFactory(new MapValueFactory<>("email"));
        phoneColumn.setCellValueFactory(new MapValueFactory<>("phone"));
        addressColumn.setCellValueFactory(new MapValueFactory<>("address"));
        enterTimeColumn.setCellValueFactory(new MapValueFactory<>("enterTime"));

        // 设置表格选择监听器
        dataTableView.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            if (newSelection != null) {
                changeTeacherInfo();
            }
        });

        setTableViewData();
        genderList = HttpRequestUtil.getDictionaryOptionItemList("XBM");

        genderComboBox.getItems().addAll(genderList);
        birthdayPick.setConverter(new LocalDateStringConverter("yyyy-MM-dd"));
        enterTimePick.setConverter(new LocalDateStringConverter("yyyy-MM-dd"));
    }

    /**
     * 清除学生表单中输入信息
     */
    public void clearPanel() {
        personId = null;
        numField.setText("");
        nameField.setText("");
        deptField.setText("");
        titleField.setText("");
        degreeField.setText("");
        cardField.setText("");
        genderComboBox.getSelectionModel().select(-1);
        birthdayPick.getEditor().setText("");
        emailField.setText("");
        phoneField.setText("");
        addressField.setText("");
        enterTimePick.getEditor().setText("");
    }

    protected void changeTeacherInfo() {
        Map<String,Object> form = dataTableView.getSelectionModel().getSelectedItem();
        if (form == null) {
            clearPanel();
            return;
        }
        // 打印选中行的数据，用于调试
        System.out.println("选中行数据: " + form);

        personId = CommonMethod.getInteger(form, "personId");
        if (personId == null) {
            System.out.println("错误: personId为空");
            return;
        }

        System.out.println("请求教师信息: personId = " + personId);

        DataRequest req = new DataRequest();
        req.add("personId", personId);
        DataResponse res = HttpRequestUtil.request("/api/teacher/getTeacherInfo", req);

        if (res == null || res.getCode() != 0) {
            if (res != null) {
                System.out.println("请求错误: " + res.getMsg());
                MessageDialog.showDialog(res.getMsg());
            } else {
                System.out.println("请求错误: 获取教师信息失败");
                MessageDialog.showDialog("获取教师信息失败");
            }
            return;
        }

        form = (Map) res.getData();
        System.out.println("获取到教师信息: " + form);

        numField.setText(CommonMethod.getString(form, "num"));
        nameField.setText(CommonMethod.getString(form, "name"));
        deptField.setText(CommonMethod.getString(form, "dept"));
        titleField.setText(CommonMethod.getString(form, "title"));
        degreeField.setText(CommonMethod.getString(form, "degree"));
        cardField.setText(CommonMethod.getString(form, "card"));
        genderComboBox.getSelectionModel().select(CommonMethod.getOptionItemIndexByValue(genderList, CommonMethod.getString(form, "gender")));
        birthdayPick.getEditor().setText(CommonMethod.getString(form, "birthday"));
        emailField.setText(CommonMethod.getString(form, "email"));
        phoneField.setText(CommonMethod.getString(form, "phone"));
        addressField.setText(CommonMethod.getString(form, "address"));
        enterTimePick.getEditor().setText(CommonMethod.getString(form, "enterTime"));
        // 添加调试信息
        System.out.println("已加载教师信息: " + personId + " - " + CommonMethod.getString(form, "name"));
    }

    // 表格选择监听器已在initialize方法中设置

    /**
     * 点击查询按钮，从从后台根据输入的串，查询匹配的教师在教师列表中显示
     */
    @FXML
    protected void onQueryButtonClick() {
        String numName = numNameTextField.getText();

        if (numName == null || numName.trim().isEmpty()) {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("提示");
            alert.setHeaderText(null);
            alert.setContentText("请输入教师编号或姓名");
            alert.showAndWait();
            return;
        }

        DataRequest req = new DataRequest();
        req.add("numName", numName);
        DataResponse res = HttpRequestUtil.request("/api/teacher/getTeacherList", req);

        if (res != null && res.getCode() == 0) {
            ArrayList<Map> queryResults = (ArrayList<Map>) res.getData();

            if (queryResults.isEmpty()) {
                Alert alert = new Alert(Alert.AlertType.INFORMATION);
                alert.setTitle("提示");
                alert.setHeaderText(null);
                alert.setContentText("未找到符合条件的教师");
                alert.showAndWait();
                return;
            }

            // 显示查询结果窗口
            showTeacherQueryResultWindow(queryResults, numName);
        } else {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("错误");
            alert.setHeaderText(null);
            alert.setContentText("查询教师失败");
            alert.showAndWait();
        }
    }

    /**
     * 显示教师查询结果窗口
     */
    private void showTeacherQueryResultWindow(ArrayList<Map> queryResults, String queryText) {
        try {
            // 创建新窗口
            Stage stage = new Stage();
            stage.setTitle("教师查询结果: " + queryText);

            // 创建表格
            TableView<Map> resultTableView = new TableView<>();

            // 创建列
            TableColumn<Map, Object> numCol = new TableColumn<>("教师编号");
            numCol.setCellValueFactory(new MapValueFactory<>("num"));
            numCol.setPrefWidth(120.0);

            TableColumn<Map, Object> nameCol = new TableColumn<>("姓名");
            nameCol.setCellValueFactory(new MapValueFactory<>("name"));
            nameCol.setPrefWidth(100.0);

            TableColumn<Map, Object> deptCol = new TableColumn<>("学院");
            deptCol.setCellValueFactory(new MapValueFactory<>("dept"));
            deptCol.setPrefWidth(150.0);

            TableColumn<Map, Object> titleCol = new TableColumn<>("职称");
            titleCol.setCellValueFactory(new MapValueFactory<>("title"));
            titleCol.setPrefWidth(120.0);

            TableColumn<Map, Object> degreeCol = new TableColumn<>("学位");
            degreeCol.setCellValueFactory(new MapValueFactory<>("degree"));
            degreeCol.setPrefWidth(100.0);

            TableColumn<Map, Object> genderCol = new TableColumn<>("性别");
            genderCol.setCellValueFactory(new MapValueFactory<>("genderName"));
            genderCol.setPrefWidth(80.0);

            TableColumn<Map, Object> emailCol = new TableColumn<>("邮箱");
            emailCol.setCellValueFactory(new MapValueFactory<>("email"));
            emailCol.setPrefWidth(150.0);

            TableColumn<Map, Object> enterTimeCol = new TableColumn<>("入职时间");
            enterTimeCol.setCellValueFactory(new MapValueFactory<>("enterTime"));
            enterTimeCol.setPrefWidth(100.0);

            // 添加列到表格
            resultTableView.getColumns().addAll(numCol, nameCol, deptCol, titleCol, degreeCol, genderCol, emailCol, enterTimeCol);

            // 设置数据
            ObservableList<Map> data = FXCollections.observableArrayList(queryResults);
            resultTableView.setItems(data);

            // 创建布局
            BorderPane borderPane = new BorderPane();
            borderPane.setCenter(resultTableView);
            borderPane.setPadding(new Insets(20));

            // 设置场景
            Scene scene = new Scene(borderPane, 900, 600);
            stage.setScene(scene);

            // 窗口关闭时的事件
            stage.setOnHidden(event -> {
                // 刷新主界面的教师列表，显示所有教师
                loadAllTeachers();
            });

            // 显示窗口
            stage.show();
        } catch (Exception e) {
            e.printStackTrace();
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("错误");
            alert.setHeaderText(null);
            alert.setContentText("显示查询结果窗口失败: " + e.getMessage());
            alert.showAndWait();
        }
    }

    /**
     * 加载所有教师
     */
    private void loadAllTeachers() {
        DataRequest req = new DataRequest();
        req.add("numName", "");
        DataResponse res = HttpRequestUtil.request("/api/teacher/getTeacherList", req);

        if (res != null && res.getCode() == 0) {
            teacherList = (ArrayList<Map>) res.getData();
            setTableViewData();
        }
    }


    /**
     * 添加新学生， 清空输入信息， 输入相关信息，点击保存即可添加新的学生
     */
    @FXML
    protected void onAddButtonClick() {
        clearPanel();
    }

    /**
     * 点击删除按钮 删除当前编辑的学生的数据
     */
    @FXML
    protected void onDeleteButtonClick() {
        Map form = dataTableView.getSelectionModel().getSelectedItem();
        if (form == null) {
            MessageDialog.showDialog("没有选择，不能删除");
            return;
        }
        int ret = MessageDialog.choiceDialog("确认要删除吗?");
        if (ret != MessageDialog.CHOICE_YES) {
            return;
        }
        personId = CommonMethod.getInteger(form, "personId");
        DataRequest req = new DataRequest();
        req.add("personId", personId);
        DataResponse res = HttpRequestUtil.request("/api/teacher/teacherDelete", req);
        if(res!= null) {
            if (res.getCode() == 0) {
                MessageDialog.showDialog("删除成功！");
                loadAllTeachers(); // 直接加载所有教师，不要弹出查询窗口
            } else {
                MessageDialog.showDialog(res.getMsg());
            }
        }
    }

    /**
     * 点击保存按钮，保存当前编辑的学生信息，如果是新添加的学生，后台添加学生
     */
    @FXML
    protected void onSaveButtonClick() {
        if (numField.getText().isEmpty()) {
            MessageDialog.showDialog("工号为空，不能修改");
            return;
        }
        Map<String,Object> form = new HashMap<>();
        form.put("num", numField.getText());
        form.put("name", nameField.getText());
        form.put("dept", deptField.getText());
        form.put("title", titleField.getText());
        form.put("degree", degreeField.getText());
        form.put("card", cardField.getText());
        if (genderComboBox.getSelectionModel() != null && genderComboBox.getSelectionModel().getSelectedItem() != null)
            form.put("gender", genderComboBox.getSelectionModel().getSelectedItem().getValue());
        form.put("birthday", birthdayPick.getEditor().getText());
        form.put("email", emailField.getText());
        form.put("phone", phoneField.getText());
        form.put("address", addressField.getText());
        form.put("enterTime", enterTimePick.getEditor().getText());
        DataRequest req = new DataRequest();
        req.add("personId", personId);
        req.add("form", form);
        DataResponse res = HttpRequestUtil.request("/api/teacher/teacherEditSave", req);
        if (res.getCode() == 0) {
            personId = CommonMethod.getIntegerFromObject(res.getData());
            MessageDialog.showDialog("提交成功！");
            loadAllTeachers(); // 直接加载所有教师，不要弹出查询窗口
        } else {
            MessageDialog.showDialog(res.getMsg());
        }
    }

    /**
     * doNew() doSave() doDelete() 重写 ToolController 中的方法， 实现选择 新建，保存，删除 对学生的增，删，改操作
     */
    public void doNew() {
        clearPanel();
    }

    public void doSave() {
        onSaveButtonClick();
    }

    public void doDelete() {
        onDeleteButtonClick();
    }

    /**
     * 导出学生信息表的示例 重写ToolController 中的doExport 这里给出了一个导出学生基本信息到Excl表的示例， 后台生成Excl文件数据，传回前台，前台将文件保存到本地
     */
    public void doExport() {
        String numName = numNameTextField.getText();
        DataRequest req = new DataRequest();
        req.add("numName", numName);
        byte[] bytes = HttpRequestUtil.requestByteData("/api/teacher/getTeacherListExcl", req);
        if (bytes != null) {
            try {
                FileChooser fileDialog = new FileChooser();
                fileDialog.setTitle("前选择保存的文件");
                fileDialog.setInitialDirectory(new File("C:/"));
                fileDialog.getExtensionFilters().addAll(
                        new FileChooser.ExtensionFilter("XLSX 文件", "*.xlsx"));
                File file = fileDialog.showSaveDialog(null);
                if (file != null) {
                    FileOutputStream out = new FileOutputStream(file);
                    out.write(bytes);
                    out.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    // 导入功能已移除

//    @FXML
//    protected void onFamilyButtonClick() {
//        if(personId == null) {
//            MessageDialog.showDialog("请先选择学生");
//            return;
//        }
//
//        DataRequest req = new DataRequest();
//        req.add("personId", personId);
//        DataResponse res = HttpRequestUtil.request("/api/student/getFamilyMemberList", req);
//        if (res.getCode() != 0) {
//            MessageDialog.showDialog(res.getMsg());
//            return;
//        }
//
//        List<Map> familyList = (List<Map>) res.getData();
//        ObservableList<Map> oList = FXCollections.observableArrayList(familyList);
//
//        Stage stage = new Stage();
//        TableView<Map> table = new TableView<>(oList);
//        table.setEditable(true);
//
//        // 添加表格列
//        TableColumn<Map, String> relationCol = new TableColumn<>("关系");
//        relationCol.setCellValueFactory(new MapValueFactory("relation"));
//        relationCol.setCellFactory(TextFieldTableCell.forTableColumn());
//
//        TableColumn<Map, String> nameCol = new TableColumn<>("姓名");
//        nameCol.setCellValueFactory(new MapValueFactory("name"));
//        nameCol.setCellFactory(TextFieldTableCell.forTableColumn());
//
//        TableColumn<Map, String> genderCol = new TableColumn<>("性别");
//        genderCol.setCellValueFactory(new MapValueFactory("gender"));
//        genderCol.setCellFactory(TextFieldTableCell.forTableColumn());
//
//        TableColumn<Map, String> ageCol = new TableColumn<>("年龄");
//        ageCol.setCellValueFactory(new MapValueFactory("age"));
//        ageCol.setCellFactory(TextFieldTableCell.forTableColumn());
//
//        TableColumn<Map, String> unitCol = new TableColumn<>("单位");
//        unitCol.setCellValueFactory(new MapValueFactory("unit"));
//        unitCol.setCellFactory(TextFieldTableCell.forTableColumn());
//
//        table.getColumns().addAll(relationCol, nameCol, genderCol, ageCol, unitCol);
//
//        // 添加操作按钮
//        FlowPane buttonPane = new FlowPane(10, 10);
//        Button addButton = new Button("添加");
//        Button saveButton = new Button("保存");
//        Button deleteButton = new Button("删除");
//
//        addButton.setOnAction(e -> {
//            Map<String, Object> newMember = new HashMap<>();
//            newMember.put("personId", personId);
//            newMember.put("relation", "");
//            newMember.put("name", "");
//            newMember.put("gender", "");
//            newMember.put("age", 0);
//            newMember.put("unit", "");
//            oList.add(newMember);
//        });
//
//        saveButton.setOnAction(e -> {
//            for(Map member : oList) {
//                DataRequest saveReq = new DataRequest();
//                saveReq.add("form", member);
//                DataResponse saveRes = HttpRequestUtil.request("/api/student/familyMemberSave", saveReq);
//                if(saveRes.getCode() != 0) {
//                    MessageDialog.showDialog(saveRes.getMsg());
//                    return;
//                }
//            }
//            MessageDialog.showDialog("保存成功");
//        });
//
//        deleteButton.setOnAction(e -> {
//            Map selected = table.getSelectionModel().getSelectedItem();
//            if(selected == null) {
//                MessageDialog.showDialog("请选择要删除的成员");
//                return;
//            }
//
//            Integer memberId = CommonMethod.getInteger(selected, "memberId");
//            if(memberId != null) {
//                DataRequest delReq = new DataRequest();
//                delReq.add("memberId", memberId);
//                DataResponse delRes = HttpRequestUtil.request("/api/student/familyMemberDelete", delReq);
//                if(delRes.getCode() != 0) {
//                    MessageDialog.showDialog(delRes.getMsg());
//                    return;
//                }
//            }
//            oList.remove(selected);
//        });
//
//        buttonPane.getChildren().addAll(addButton, saveButton, deleteButton);
//
//        BorderPane root = new BorderPane();
//        root.setCenter(table);
//        root.setBottom(buttonPane);
//
//        Scene scene = new Scene(root, 600, 400);
//        stage.setScene(scene);
//        stage.setTitle("家庭成员管理");
//        stage.initModality(Modality.APPLICATION_MODAL);
//        stage.showAndWait();
//    }
    //    public void displayPhoto(){
//        DataRequest req = new DataRequest();
//        req.add("fileName", "photo/" + personId + ".jpg");  //个人照片显示
//        byte[] bytes = HttpRequestUtil.requestByteData("/api/base/getFileByteData", req);
//        if (bytes != null) {
//            ByteArrayInputStream in = new ByteArrayInputStream(bytes);
//            Image img = new Image(in);
//            photoImageView.setImage(img);
//        }
//
//    }
//
//    @FXML
//    public void onPhotoButtonClick(){
//        FileChooser fileDialog = new FileChooser();
//        fileDialog.setTitle("图片上传");
//        fileDialog.setInitialDirectory(new File("C:/"));
//        fileDialog.getExtensionFilters().addAll(
//                new FileChooser.ExtensionFilter("JPG 文件", "*.jpg"));
//        File file = fileDialog.showOpenDialog(null);
//        if(file == null)
//            return;
//        DataResponse res =HttpRequestUtil.uploadFile("/api/base/uploadPhoto",file.getPath(),"photo/" + personId + ".jpg");
//        if(res.getCode() == 0) {
//            MessageDialog.showDialog("上传成功！");
//            displayPhoto();
//        }
//        else {
//            MessageDialog.showDialog(res.getMsg());
//        }
//    }
//public void displayPhoto(){
//    if (personId != null) {
//        DataRequest req = new DataRequest();
//        req.add("fileName", "photo/" + personId + ".jpg");  //个人照片显示
//        byte[] bytes = HttpRequestUtil.requestByteData("/api/base/getFileByteData", req);
//        if (bytes != null) {
//            ByteArrayInputStream in = new ByteArrayInputStream(bytes);
//            Image img = new Image(in);
//            photoImageView.setImage(img);
//        }
//    }
//}
    // 照片相关功能已移除
//    @FXML
//    public void onImportFeeButtonClick(){
//        FileChooser fileDialog = new FileChooser();
//        fileDialog.setTitle("前选择消费数据表");
//        fileDialog.setInitialDirectory(new File("D:/"));
//        fileDialog.getExtensionFilters().addAll(
//                new FileChooser.ExtensionFilter("XLSX 文件", "*.xlsx"));
//        File file = fileDialog.showOpenDialog(null);
//        String paras = "personId="+personId;
//        DataResponse res =HttpRequestUtil.importData("/api/student/importFeeData",file.getPath(),paras);
//        if(res.getCode() == 0) {
//            MessageDialog.showDialog("上传成功！");
//        }
//        else {
//            MessageDialog.showDialog(res.getMsg());
//        }
//    }
//    @FXML
//    protected void onQueryButtonClick() {
//        String numName = numNameTextField.getText();
//        DataRequest req = new DataRequest();
//        req.add("numName", numName);
//        req.add("pageNum", 1);
//        req.add("pageSize", 10);
//
//        DataResponse res = HttpRequestUtil.request("/api/student/getStudentPageData", req);
//        if (res != null && res.getCode() == 0) {
//            Map data = (Map)res.getData();
//            studentList = (ArrayList<Map>) data.get("dataList");
//            setTableViewData();
//        }
//    }

}
