<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.FlowPane?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>

<VBox alignment="CENTER" spacing="20.0" xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.teach.javafx.controller.DurationEditController">
    <padding>
        <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
    </padding>
    <GridPane vgap="5.0">
        <columnConstraints>
            <ColumnConstraints halignment="RIGHT" hgrow="SOMETIMES" maxWidth="59.5" minWidth="10.0" prefWidth="59.5" />
            <ColumnConstraints hgrow="SOMETIMES" maxWidth="140.5" minWidth="10.0" prefWidth="140.5" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
        </rowConstraints>
        <children>
            <Label text="学生" GridPane.columnIndex="0" GridPane.rowIndex="0">
                <GridPane.margin>
                    <Insets right="5.0" />
                </GridPane.margin>
            </Label>
            <ComboBox fx:id="studentComboBox" GridPane.columnIndex="1" GridPane.rowIndex="0" />
            <Label text="志愿活动" GridPane.columnIndex="0" GridPane.rowIndex="1"> <!-- 奖项改为志愿活动 -->
                <GridPane.margin>
                    <Insets right="5.0" />
                </GridPane.margin>
            </Label>
            <ComboBox fx:id="volunteeringComboBox" GridPane.columnIndex="1" GridPane.rowIndex="1" /> <!-- prizeComboBox改为volunteeringComboBox -->
            <Label text="时长" GridPane.columnIndex="0" GridPane.rowIndex="2"> <!-- 分数改为时长 -->
                <GridPane.margin>
                    <Insets right="5.0" />
                </GridPane.margin>
            </Label>
            <TextField fx:id="markField" GridPane.columnIndex="1" GridPane.rowIndex="2" /> <!-- markField改为hoursField -->
        </children>
    </GridPane>
    <FlowPane alignment="CENTER" prefHeight="40.0" prefWidth="300.0" BorderPane.alignment="CENTER">
        <children>
            <Button mnemonicParsing="false" onAction="#okButtonClick" text="确认">
                <FlowPane.margin>
                    <Insets right="10.0" />
                </FlowPane.margin>
            </Button>
            <Button mnemonicParsing="false" onAction="#cancelButtonClick" text="取消">
                <FlowPane.margin>
                    <Insets left="10.0" />
                </FlowPane.margin>
            </Button>
        </children>
    </FlowPane>
</VBox>