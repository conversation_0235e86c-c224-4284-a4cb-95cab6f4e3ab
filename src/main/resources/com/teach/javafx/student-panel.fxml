<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.SplitPane?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.Region?>

<BorderPane xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1"
            style="-fx-background-color: #f8f9fa;" fx:controller="com.teach.javafx.controller.StudentController">
   <top>
        <VBox spacing="15" style="-fx-background-color: white; -fx-padding: 20; -fx-border-color: #e0e0e0; -fx-border-width: 0 0 1 0; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 1);">
            <!-- 标题区域 -->
            <HBox alignment="CENTER_LEFT" spacing="8">
                <children>
                    <Label text="👨‍🎓" style="-fx-font-size: 20;"/>
                    <Label text="学生管理" style="-fx-font-size: 24; -fx-font-weight: bold; -fx-text-fill: #2c3e50;"/>
                    <Region HBox.hgrow="ALWAYS"/>
                    <Label text="管理学生基本信息" style="-fx-font-size: 12; -fx-text-fill: #7f8c8d;"/>
                </children>
            </HBox>

            <!-- 操作区域 -->
            <HBox spacing="15" alignment="CENTER_LEFT">
                <children>
                    <Button onAction="#onAddButtonClick" text="添加学生"
                            style="-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 10 20; -fx-cursor: hand;"/>
                    <Button onAction="#onDeleteButtonClick" text="删除学生"
                            style="-fx-background-color: #f44336; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 10 20; -fx-cursor: hand;"/>
                    <Region HBox.hgrow="ALWAYS"/>
                    <Label text="学号/姓名:" style="-fx-font-weight: bold; -fx-text-fill: #555;" />
                    <TextField fx:id="numNameTextField" promptText="输入学号或姓名" prefWidth="150"
                               style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>
                    <Button onAction="#onQueryButtonClick" text="查询学生"
                            style="-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 10 20; -fx-cursor: hand;"/>
                </children>
            </HBox>
        </VBox>
   </top>
   <center>
      <SplitPane dividerPositions="0.65" style="-fx-background-color: transparent;">
        <items>
            <TableView fx:id="dataTableView" style="-fx-background-color: white; -fx-border-color: #e0e0e0; -fx-border-width: 1; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 1);">
                <columns>
                    <TableColumn fx:id="numColumn" prefWidth="120.0" text="学号" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                    <TableColumn fx:id="nameColumn" prefWidth="100.0" text="姓名" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                    <TableColumn fx:id="deptColumn" prefWidth="120.0" text="院系" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                    <TableColumn fx:id="majorColumn" prefWidth="120.0" text="专业" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                    <TableColumn fx:id="classNameColumn" prefWidth="100.0" text="班级" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                    <TableColumn fx:id="cardColumn" prefWidth="150.0" text="证件号码" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                    <TableColumn fx:id="genderColumn" prefWidth="80.0" text="性别" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                    <TableColumn fx:id="birthdayColumn" prefWidth="120.0" text="出生日期" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                    <TableColumn fx:id="emailColumn" prefWidth="150.0" text="邮箱" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                    <TableColumn fx:id="phoneColumn" prefWidth="120.0" text="电话" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                    <TableColumn fx:id="addressColumn" prefWidth="180.0" text="地址" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                </columns>
                <padding>
                    <Insets top="10" right="10" bottom="10" left="10"/>
                </padding>
            </TableView>
            <VBox alignment="TOP_CENTER" spacing="15.0" style="-fx-background-color: white; -fx-border-color: #e0e0e0; -fx-border-width: 1; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 1);">
                <padding>
                    <Insets bottom="25.0" left="25.0" right="25.0" top="25.0" />
                </padding>

                <!-- 表单标题 -->
                <Label text="学生信息编辑" style="-fx-font-size: 18; -fx-font-weight: bold; -fx-text-fill: #2c3e50;"/>

                <GridPane hgap="15.0" vgap="15.0" style="-fx-padding: 10;">
                    <columnConstraints>
                        <ColumnConstraints halignment="RIGHT" hgrow="SOMETIMES" maxWidth="100.0" minWidth="80.0" prefWidth="90.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="250.0" minWidth="200.0" prefWidth="220.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="120.0" minWidth="100.0" prefWidth="110.0" />
                    </columnConstraints>
                    <rowConstraints>
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                    </rowConstraints>
                    <children>
                        <Label text="学号:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="0" />
                        <TextField fx:id="numField" promptText="请输入学号" GridPane.columnIndex="1" GridPane.rowIndex="0"
                                   style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>

                        <Label text="姓名:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="1" />
                        <TextField fx:id="nameField" promptText="请输入姓名" GridPane.columnIndex="1" GridPane.rowIndex="1"
                                   style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>

                        <Label text="院系:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="2" />
                        <TextField fx:id="deptField" promptText="请输入院系" GridPane.columnIndex="1" GridPane.rowIndex="2"
                                   style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>

                        <Label text="专业:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="3" />
                        <TextField fx:id="majorField" promptText="请输入专业" GridPane.columnIndex="1" GridPane.rowIndex="3"
                                   style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>

                        <Label text="班级:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="4" />
                        <TextField fx:id="classNameField" promptText="请输入班级" GridPane.columnIndex="1" GridPane.rowIndex="4"
                                   style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>

                        <Label text="证件号码:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="5" />
                        <TextField fx:id="cardField" promptText="请输入证件号码" GridPane.columnIndex="1" GridPane.rowIndex="5"
                                   style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>

                        <Label text="性别:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="6" />
                        <ComboBox fx:id="genderComboBox" promptText="请选择性别" GridPane.columnIndex="1" GridPane.rowIndex="6"
                                  style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 7;"/>

                        <Label text="出生日期:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="7" />
                        <DatePicker fx:id="birthdayPick" promptText="请选择出生日期" GridPane.columnIndex="1" GridPane.rowIndex="7"
                                    style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 7;"/>

                        <Label text="邮箱:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="8" />
                        <TextField fx:id="emailField" promptText="请输入邮箱地址" GridPane.columnIndex="1" GridPane.rowIndex="8"
                                   style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>

                        <Label text="电话:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="9" />
                        <TextField fx:id="phoneField" promptText="请输入电话号码" GridPane.columnIndex="1" GridPane.rowIndex="9"
                                   style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>

                        <Label text="地址:" style="-fx-font-weight: bold; -fx-text-fill: #555;" GridPane.rowIndex="10" />
                        <TextField fx:id="addressField" promptText="请输入详细地址" GridPane.columnIndex="1" GridPane.rowIndex="10"
                                   style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>

                        <!-- 照片区域 -->
                        <Button fx:id="photoButton" text="点击上传照片" onAction="#onPhotoButtonClick"
                                GridPane.columnIndex="2" GridPane.rowIndex="0" GridPane.rowSpan="4"
                                style="-fx-background-color: #f8f9fa; -fx-border-color: #ddd; -fx-border-width: 2; -fx-border-style: dashed; -fx-background-radius: 4; -fx-border-radius: 4; -fx-text-fill: #666; -fx-font-size: 12; -fx-cursor: hand;"/>
                    </children>
                </GridPane>

                <!-- 操作按钮区域 -->
                <HBox spacing="15" alignment="CENTER" style="-fx-padding: 15 0 0 0;">
                    <children>
                        <Button onAction="#onSaveButtonClick" text="保存学生信息"
                                style="-fx-background-color: #FF9800; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 12 25; -fx-cursor: hand;"/>
                        <Button onAction="#onFamilyButtonClick" text="家庭信息管理"
                                style="-fx-background-color: #9C27B0; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 12 25; -fx-cursor: hand;"/>
                    </children>
                </HBox>
            </VBox>
        </items>
      </SplitPane>
      <BorderPane.margin>
          <Insets top="20" right="20" bottom="20" left="20"/>
      </BorderPane.margin>
   </center>
</BorderPane>
